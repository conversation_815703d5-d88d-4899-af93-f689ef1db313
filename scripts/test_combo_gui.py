#!/usr/bin/env python3
"""
创建一个简单的GUI应用来测试combo box监听功能
"""

import tkinter as tk
from tkinter import ttk
import threading
import time

class ComboTestApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Combo Box 测试应用")
        self.root.geometry("400x300")
        
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        title_label = ttk.Label(main_frame, text="Combo Box 监听测试", font=("Arial", 16))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # 说明文本
        instruction_text = """
请按以下步骤测试:
1. 点击下面的combo box
2. 观察终端输出的文件变化
3. 选择一个选项或点击其他地方关闭combo box
4. 验证文件是否立即清空
        """
        instruction_label = ttk.Label(main_frame, text=instruction_text, justify=tk.LEFT)
        instruction_label.grid(row=1, column=0, columnspan=2, pady=(0, 20))
        
        # Combo Box 1
        ttk.Label(main_frame, text="测试 Combo Box 1:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.combo1 = ttk.Combobox(main_frame, values=["选项1", "选项2", "选项3", "选项4", "选项5"])
        self.combo1.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        
        # Combo Box 2
        ttk.Label(main_frame, text="测试 Combo Box 2:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.combo2 = ttk.Combobox(main_frame, values=["Apple", "Banana", "Cherry", "Date", "Elderberry"])
        self.combo2.grid(row=3, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        
        # Combo Box 3 (可编辑)
        ttk.Label(main_frame, text="可编辑 Combo Box:").grid(row=4, column=0, sticky=tk.W, pady=5)
        self.combo3 = ttk.Combobox(main_frame, values=["Red", "Green", "Blue", "Yellow", "Purple"])
        self.combo3.grid(row=4, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        self.combo3['state'] = 'normal'  # 可编辑
        
        # 状态显示
        self.status_var = tk.StringVar()
        self.status_var.set("等待combo box操作...")
        status_label = ttk.Label(main_frame, textvariable=self.status_var, foreground="blue")
        status_label.grid(row=5, column=0, columnspan=2, pady=(20, 10))
        
        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=6, column=0, columnspan=2, pady=10)
        
        ttk.Button(button_frame, text="清空所有选择", command=self.clear_all).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="退出", command=self.root.quit).pack(side=tk.LEFT, padx=5)
        
        # 配置列权重
        main_frame.columnconfigure(1, weight=1)
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        
        # 绑定事件
        self.bind_events()
        
        # 启动状态更新线程
        self.start_status_update()
    
    def bind_events(self):
        """绑定combo box事件"""
        # 绑定下拉事件
        self.combo1.bind('<<ComboboxSelected>>', lambda e: self.on_combo_selected("Combo1", self.combo1.get()))
        self.combo2.bind('<<ComboboxSelected>>', lambda e: self.on_combo_selected("Combo2", self.combo2.get()))
        self.combo3.bind('<<ComboboxSelected>>', lambda e: self.on_combo_selected("Combo3", self.combo3.get()))
        
        # 绑定焦点事件
        self.combo1.bind('<FocusIn>', lambda e: self.on_combo_focus("Combo1", "获得焦点"))
        self.combo1.bind('<FocusOut>', lambda e: self.on_combo_focus("Combo1", "失去焦点"))
        self.combo2.bind('<FocusIn>', lambda e: self.on_combo_focus("Combo2", "获得焦点"))
        self.combo2.bind('<FocusOut>', lambda e: self.on_combo_focus("Combo2", "失去焦点"))
        self.combo3.bind('<FocusIn>', lambda e: self.on_combo_focus("Combo3", "获得焦点"))
        self.combo3.bind('<FocusOut>', lambda e: self.on_combo_focus("Combo3", "失去焦点"))
    
    def on_combo_selected(self, combo_name, value):
        """combo box选择事件处理"""
        self.status_var.set(f"{combo_name} 选择了: {value}")
        print(f"[GUI] {combo_name} 选择了: {value}")
    
    def on_combo_focus(self, combo_name, action):
        """combo box焦点事件处理"""
        self.status_var.set(f"{combo_name} {action}")
        print(f"[GUI] {combo_name} {action}")
    
    def clear_all(self):
        """清空所有combo box的选择"""
        self.combo1.set("")
        self.combo2.set("")
        self.combo3.set("")
        self.status_var.set("已清空所有选择")
        print("[GUI] 已清空所有combo box选择")
    
    def start_status_update(self):
        """启动状态更新线程"""
        def update_status():
            while True:
                try:
                    # 检查文件状态
                    import os
                    file_path = "/tmp/.recordmenu1.txt"
                    if os.path.exists(file_path):
                        with open(file_path, 'r', encoding='UTF-8') as f:
                            content = f.read().strip()
                        if content:
                            # 文件有内容时更新状态
                            pass
                    time.sleep(1)
                except:
                    pass
        
        status_thread = threading.Thread(target=update_status, daemon=True)
        status_thread.start()
    
    def run(self):
        """运行应用"""
        print("启动Combo Box测试GUI...")
        print("请在GUI中操作combo box，同时观察终端的监听输出")
        self.root.mainloop()

if __name__ == "__main__":
    app = ComboTestApp()
    app.run()
