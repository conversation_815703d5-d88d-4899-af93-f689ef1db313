#!/usr/bin/env python3
"""
验证combo box精确监听实现的脚本
"""

import ast
import inspect
from listenHF import AppSpecificMenuMonitor

def analyze_implementation():
    """分析实现的功能"""
    print("=" * 60)
    print("Combo Box 精确监听实现分析")
    print("=" * 60)
    
    # 创建监控器实例
    monitor = AppSpecificMenuMonitor()
    
    # 检查新增的属性
    print("1. 新增的状态跟踪属性:")
    if hasattr(monitor, 'combo_box_states'):
        print("   ✓ combo_box_states: 跟踪各个combo box的状态")
    else:
        print("   ✗ combo_box_states: 缺失")
        
    if hasattr(monitor, 'combo_box_lock'):
        print("   ✓ combo_box_lock: 防止并发问题的锁")
    else:
        print("   ✗ combo_box_lock: 缺失")
    
    print()
    
    # 检查新增的方法
    print("2. 新增的方法:")
    methods_to_check = [
        ('get_combo_box_id', '获取combo box唯一标识符'),
        ('is_combo_box_expanded', '检查combo box是否展开'),
        ('handle_combo_box_event', '处理combo box事件'),
        ('write_combo_box_content', '写入combo box内容'),
        ('clear_combo_box_content', '清空combo box内容')
    ]
    
    for method_name, description in methods_to_check:
        if hasattr(monitor, method_name):
            print(f"   ✓ {method_name}: {description}")
        else:
            print(f"   ✗ {method_name}: 缺失")
    
    print()
    
    # 检查方法实现细节
    print("3. 方法实现分析:")
    
    # 分析get_combo_box_id方法
    if hasattr(monitor, 'get_combo_box_id'):
        method = getattr(monitor, 'get_combo_box_id')
        source = inspect.getsource(method)
        if 'app_name' in source and 'window_name' in source and 'position' in source:
            print("   ✓ get_combo_box_id: 使用应用名、窗口名、控件名和位置生成唯一ID")
        else:
            print("   ⚠ get_combo_box_id: 实现可能不完整")
    
    # 分析is_combo_box_expanded方法
    if hasattr(monitor, 'is_combo_box_expanded'):
        method = getattr(monitor, 'is_combo_box_expanded')
        source = inspect.getsource(method)
        if 'STATE_EXPANDED' in source:
            print("   ✓ is_combo_box_expanded: 正确检查STATE_EXPANDED状态")
        else:
            print("   ⚠ is_combo_box_expanded: 可能未正确检查展开状态")
    
    # 分析handle_combo_box_event方法
    if hasattr(monitor, 'handle_combo_box_event'):
        method = getattr(monitor, 'handle_combo_box_event')
        source = inspect.getsource(method)
        if 'previous_state' in source and 'is_expanded' in source:
            print("   ✓ handle_combo_box_event: 正确比较状态变化")
        else:
            print("   ⚠ handle_combo_box_event: 状态变化逻辑可能有问题")
    
    print()
    
    # 检查事件监听器的修改
    print("4. 事件监听器修改:")
    if hasattr(monitor, 'event_listener'):
        method = getattr(monitor, 'event_listener')
        source = inspect.getsource(method)
        if 'handle_combo_box_event' in source:
            print("   ✓ event_listener: 已集成新的combo box处理逻辑")
        else:
            print("   ✗ event_listener: 未集成新的处理逻辑")
        
        if 'start_clear_timer' not in source or source.count('start_clear_timer') < 2:
            print("   ✓ 已移除或减少定时器依赖")
        else:
            print("   ⚠ 仍然依赖定时器机制")
    
    print()
    
    # 总结改进点
    print("5. 主要改进点:")
    print("   • 从基于固定时间的清空机制改为基于状态变化的精确控制")
    print("   • 添加combo box唯一标识符，支持多个combo box同时监听")
    print("   • 使用STATE_EXPANDED状态精确判断combo box开启/关闭")
    print("   • 保留定时器作为备用机制，提高系统稳定性")
    print("   • 添加线程安全锁，防止并发问题")
    
    print()
    print("=" * 60)
    print("实现验证完成")
    print("=" * 60)

def test_combo_box_id_generation():
    """测试combo box ID生成逻辑"""
    print("\n测试 Combo Box ID 生成:")
    print("-" * 40)
    
    # 模拟combo box对象
    class MockComboBox:
        def __init__(self, app_name, window_name, control_name, x, y):
            self.app_name = app_name
            self.window_name = window_name
            self.control_name = control_name
            self.x = x
            self.y = y
            
        def getApplication(self):
            class MockApp:
                def __init__(self, name):
                    self.name = name
            return MockApp(self.app_name)
            
        @property
        def name(self):
            return self.control_name
            
        @property
        def parent(self):
            class MockParent:
                def __init__(self, name):
                    self.name = name
                def getRoleName(self):
                    return "frame"
            return MockParent(self.window_name)
            
        def queryComponent(self):
            class MockComponent:
                def __init__(self, x, y):
                    self.x = x
                    self.y = y
                def getExtents(self, coords):
                    class MockExtents:
                        def __init__(self, x, y):
                            self.x = x
                            self.y = y
                    return MockExtents(self.x, self.y)
            return MockComponent(self.x, self.y)
    
    monitor = AppSpecificMenuMonitor()
    
    # 测试不同的combo box
    test_cases = [
        ("Firefox", "主窗口", "地址栏", 100, 50),
        ("LibreOffice", "文档窗口", "字体选择", 200, 100),
        ("Firefox", "主窗口", "地址栏", 100, 50),  # 重复测试
    ]
    
    for app, window, control, x, y in test_cases:
        mock_combo = MockComboBox(app, window, control, x, y)
        try:
            combo_id = monitor.get_combo_box_id(mock_combo)
            print(f"   {app}/{window}/{control} -> {combo_id}")
        except Exception as e:
            print(f"   {app}/{window}/{control} -> 错误: {e}")

if __name__ == "__main__":
    analyze_implementation()
    test_combo_box_id_generation()
