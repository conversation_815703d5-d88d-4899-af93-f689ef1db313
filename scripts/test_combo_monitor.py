#!/usr/bin/env python3
"""
测试combo box精确监听功能的脚本
"""

import sys
import os
import time
import threading
from listenHF import AppSpecificMenuMonitor

def monitor_file_changes(file_path, duration=30):
    """监控文件变化
    参数:
        file_path: 要监控的文件路径
        duration: 监控持续时间（秒）
    """
    print(f"开始监控文件: {file_path}")
    print(f"监控时长: {duration}秒")
    print("=" * 50)
    
    last_content = ""
    start_time = time.time()
    
    while time.time() - start_time < duration:
        try:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='UTF-8') as f:
                    current_content = f.read().strip()
                
                if current_content != last_content:
                    timestamp = time.strftime("%H:%M:%S")
                    if current_content:
                        print(f"[{timestamp}] 文件有内容 (长度: {len(current_content)})")
                        print(f"内容预览: {current_content[:100]}...")
                    else:
                        print(f"[{timestamp}] 文件已清空")
                    last_content = current_content
            else:
                if last_content != "FILE_NOT_EXISTS":
                    timestamp = time.strftime("%H:%M:%S")
                    print(f"[{timestamp}] 文件不存在")
                    last_content = "FILE_NOT_EXISTS"
                    
        except Exception as e:
            print(f"监控文件时出错: {e}")
            
        time.sleep(0.5)  # 每0.5秒检查一次
    
    print("=" * 50)
    print("文件监控结束")

def main():
    """主函数"""
    print("Combo Box 精确监听测试")
    print("=" * 50)
    print("使用说明:")
    print("1. 启动监听后，请打开任意应用程序的combo box")
    print("2. 观察文件内容的变化时机")
    print("3. 验证combo box关闭时文件是否立即清空")
    print("4. 按 Ctrl+C 停止测试")
    print("=" * 50)
    
    # 创建监控器实例
    monitor = AppSpecificMenuMonitor()
    
    # 要监控的文件路径
    file_path = "/tmp/.recordmenu1.txt"
    
    # 启动文件变化监控线程
    file_monitor_thread = threading.Thread(
        target=monitor_file_changes, 
        args=(file_path, 300),  # 监控5分钟
        daemon=True
    )
    file_monitor_thread.start()
    
    try:
        print("开始监控combo box事件...")
        monitor.start_monitoring()
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"测试过程中出错: {e}")
    finally:
        print("清理资源...")

if __name__ == "__main__":
    main()
